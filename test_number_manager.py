#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编号管理器测试脚本
用于测试编号生成器的核心功能
"""

import os
import time
import threading
from number_manager import NumberManager

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("测试基本功能")
    print("=" * 50)
    
    try:
        # 使用本地测试路径
        test_path = "./test_data"
        manager = NumberManager(shared_path=test_path)
        
        print(f"✓ 编号管理器初始化成功")
        print(f"  共享路径: {manager.shared_path}")
        print(f"  数据目录: {manager.data_dir}")
        
        # 测试设置起始编号
        if not manager.config.get("initialized", False):
            manager.set_start_number(1001)
            print(f"✓ 设置起始编号: 1001")
        
        # 测试获取状态
        status = manager.get_current_status()
        print(f"✓ 获取状态成功")
        print(f"  下一个编号: {status['next_number']}")
        
        # 测试获取单个编号
        number1 = manager.get_next_number()
        print(f"✓ 获取单个编号: {number1}")
        
        # 测试批量获取编号
        numbers = manager.get_next_number(3)
        print(f"✓ 批量获取编号: {numbers}")
        
        # 测试获取历史记录
        logs = manager.get_recent_logs()
        print(f"✓ 获取历史记录: {len(logs)} 条")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

def test_concurrent_access():
    """测试并发访问"""
    print("\n" + "=" * 50)
    print("测试并发访问")
    print("=" * 50)
    
    try:
        test_path = "./test_data"
        results = []
        errors = []
        
        def worker(worker_id):
            """工作线程"""
            try:
                manager = NumberManager(shared_path=test_path)
                for i in range(5):
                    number = manager.get_next_number()
                    results.append((worker_id, number))
                    time.sleep(0.1)  # 模拟处理时间
            except Exception as e:
                errors.append((worker_id, str(e)))
        
        # 启动3个并发线程
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker, args=(f"用户{i+1}",))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        if errors:
            print(f"✗ 发现错误: {len(errors)} 个")
            for worker_id, error in errors:
                print(f"  {worker_id}: {error}")
            return False
        
        # 检查编号是否连续且无重复
        numbers = [num for _, num in results]
        numbers.sort()
        
        print(f"✓ 并发测试完成")
        print(f"  总共获取编号: {len(numbers)} 个")
        print(f"  编号范围: {min(numbers)} ~ {max(numbers)}")
        
        # 检查是否有重复
        if len(numbers) == len(set(numbers)):
            print(f"✓ 无重复编号")
        else:
            print(f"✗ 发现重复编号")
            return False
        
        # 检查是否连续
        expected = list(range(min(numbers), max(numbers) + 1))
        if numbers == expected:
            print(f"✓ 编号连续")
        else:
            print(f"✗ 编号不连续")
            print(f"  期望: {expected}")
            print(f"  实际: {numbers}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 并发测试失败: {str(e)}")
        return False

def test_network_path():
    """测试网络路径（如果可访问）"""
    print("\n" + "=" * 50)
    print("测试网络路径")
    print("=" * 50)
    
    network_path = "\\\\192.168.2.64\\e"
    
    try:
        # 检查网络路径是否可访问
        if not os.path.exists(network_path):
            print(f"⚠ 网络路径不可访问: {network_path}")
            print("  这是正常的，如果你不在目标网络环境中")
            return True
        
        print(f"✓ 网络路径可访问: {network_path}")
        
        # 测试在网络路径上创建编号管理器
        manager = NumberManager(shared_path=network_path)
        print(f"✓ 网络编号管理器初始化成功")
        
        # 测试基本操作
        status = manager.get_current_status()
        print(f"✓ 网络状态获取成功: 下一个编号 {status['next_number']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 网络路径测试失败: {str(e)}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    import shutil
    test_path = "./test_data"
    if os.path.exists(test_path):
        shutil.rmtree(test_path)
        print(f"✓ 清理测试数据: {test_path}")

def main():
    """主测试函数"""
    print("编号生成器功能测试")
    print("测试开始时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 清理之前的测试数据
    cleanup_test_data()
    
    # 运行测试
    tests = [
        ("基本功能测试", test_basic_functionality),
        ("并发访问测试", test_concurrent_access),
        ("网络路径测试", test_network_path),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✓ {test_name} 通过")
            else:
                print(f"\n✗ {test_name} 失败")
        except Exception as e:
            print(f"\n✗ {test_name} 异常: {str(e)}")
    
    # 测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！程序可以正常使用。")
    else:
        print(f"\n⚠ 有 {total - passed} 个测试失败，请检查问题。")
    
    # 询问是否清理测试数据
    try:
        choice = input("\n是否清理测试数据？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            cleanup_test_data()
    except KeyboardInterrupt:
        print("\n用户取消操作。")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
