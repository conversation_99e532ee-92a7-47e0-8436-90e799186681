#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编号生成器打包脚本
使用PyInstaller将Python程序打包成独立的exe文件
"""

import os
import sys
import subprocess
import shutil

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        # 使用国内镜像源安装
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "pyinstaller", 
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
        ])
        return True
    except subprocess.CalledProcessError:
        print("从清华镜像安装失败，尝试默认源...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            return True
        except subprocess.CalledProcessError:
            return False

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 不显示控制台窗口
        "--name=编号生成器",              # 指定exe文件名
        "--icon=icon.ico",              # 图标文件（如果存在）
        "--add-data=number_manager.py;.",  # 包含数据文件
        "--add-data=main_gui.py;.",
        "编号生成器.py"                  # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists("icon.ico"):
        cmd = [arg for arg in cmd if not arg.startswith("--icon")]
    
    try:
        subprocess.check_call(cmd)
        print("构建成功！")
        
        # 检查输出文件
        exe_path = os.path.join("dist", "编号生成器.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"生成的exe文件: {exe_path}")
            print(f"文件大小: {file_size:.1f} MB")
            
            # 创建发布目录
            release_dir = "release"
            if os.path.exists(release_dir):
                shutil.rmtree(release_dir)
            os.makedirs(release_dir)
            
            # 复制exe文件到发布目录
            shutil.copy2(exe_path, os.path.join(release_dir, "编号生成器.exe"))
            
            # 创建使用说明
            readme_content = """# 编号生成器使用说明

## 功能介绍
这是一个为三人团队设计的互感器评审编号生成器，支持：
- 连续编号分配，避免重复
- 局域网共享，多人同时使用
- 实时状态显示和历史记录
- 冒泡消息提醒

## 使用方法
1. 双击"编号生成器.exe"启动程序
2. 首次使用时可以设置起始编号
3. 点击"获取下一个编号"获取单个编号
4. 点击"批量获取编号"可一次获取多个连续编号
5. 程序会自动保存所有操作记录

## 共享设置
- 默认共享路径：\\\\192.168.2.64\\e
- 程序会自动在共享路径下创建"编号生成器数据"文件夹
- 确保所有用户都能访问该共享路径

## 注意事项
- 请确保网络连接正常
- 不要手动修改共享文件夹中的数据文件
- 如遇问题可重启程序或联系技术支持

版本：1.0
"""
            
            with open(os.path.join(release_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
                f.write(readme_content)
            
            print(f"发布文件已准备完成，位于: {release_dir}/")
            print("可以将整个release文件夹分发给其他用户。")
            
        else:
            print("错误：未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False
    
    return True

def clean_build_files():
    """清理构建过程中的临时文件"""
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["编号生成器.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理: {dir_name}/")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"已清理: {file_name}")

def main():
    """主函数"""
    print("=" * 50)
    print("编号生成器打包工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ["编号生成器.py", "main_gui.py", "number_manager.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"错误：缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        print("PyInstaller未安装，正在安装...")
        if not install_pyinstaller():
            print("错误：无法安装PyInstaller")
            print("请手动运行: pip install pyinstaller")
            return False
        print("PyInstaller安装成功！")
    
    # 构建exe
    if build_exe():
        print("\n构建完成！")
        
        # 询问是否清理临时文件
        try:
            choice = input("\n是否清理构建临时文件？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                clean_build_files()
                print("临时文件已清理。")
        except KeyboardInterrupt:
            print("\n用户取消操作。")
        
        return True
    else:
        print("\n构建失败！")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 50)
    if success:
        print("打包完成！可以在release文件夹中找到可执行文件。")
    else:
        print("打包失败！请检查错误信息并重试。")
    print("=" * 50)
    
    # 等待用户按键
    try:
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        pass
