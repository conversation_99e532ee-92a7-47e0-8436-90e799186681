#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精美的编号生成器启动器
参考现代化应用设计风格
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
from pathlib import Path

class ModernLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("互感器评审编号生成器")
        self.root.geometry("650x500")
        self.root.configure(bg='#f8fafc')
        self.root.resizable(False, False)
        
        # 设置窗口图标和居中
        self.center_window()
        self.create_widgets()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_modern_card(self, parent, bg_color='white'):
        """创建现代化卡片"""
        # 外层阴影容器
        shadow_frame = tk.Frame(parent, bg='#e2e8f0', height=2)
        shadow_frame.pack(fill=tk.X, pady=(0, 2))
        
        # 主卡片
        card = tk.Frame(parent, bg=bg_color, relief='flat', bd=0)
        return card
    
    def create_gradient_button(self, parent, text, command, bg_color='#3b82f6', hover_color='#2563eb'):
        """创建渐变按钮"""
        button = tk.Button(parent, text=text,
                          command=command,
                          font=('Microsoft YaHei UI', 12, 'bold'),
                          bg=bg_color, fg='white',
                          relief='flat', bd=0,
                          padx=40, pady=15,
                          cursor='hand2')
        
        # 悬停效果
        button.bind("<Enter>", lambda e: button.config(bg=hover_color))
        button.bind("<Leave>", lambda e: button.config(bg=bg_color))
        
        return button
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = tk.Frame(self.root, bg='#f8fafc')
        main_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=25)
        
        # 标题区域
        header_frame = tk.Frame(main_container, bg='#f8fafc')
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # 主标题
        title_label = tk.Label(header_frame, text="互感器评审编号生成器", 
                              font=('Microsoft YaHei UI', 24, 'bold'),
                              fg='#1e293b', bg='#f8fafc')
        title_label.pack()
        
        # 副标题
        subtitle_label = tk.Label(header_frame, text="选择您需要的功能模块", 
                                 font=('Microsoft YaHei UI', 12),
                                 fg='#64748b', bg='#f8fafc')
        subtitle_label.pack(pady=(8, 0))
        
        # 功能卡片容器
        cards_container = tk.Frame(main_container, bg='#f8fafc')
        cards_container.pack(fill=tk.BOTH, expand=True)
        
        # 简化版卡片
        simple_card = self.create_modern_card(cards_container)
        simple_card.pack(fill=tk.X, pady=(0, 20))
        
        simple_content = tk.Frame(simple_card, bg='white')
        simple_content.pack(pady=25, padx=30)
        
        # 简化版图标和标题
        simple_header = tk.Frame(simple_content, bg='white')
        simple_header.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(simple_header, text="🎯", 
                font=('Microsoft YaHei UI', 32),
                bg='white').pack(side=tk.LEFT)
        
        simple_title_frame = tk.Frame(simple_header, bg='white')
        simple_title_frame.pack(side=tk.LEFT, padx=(15, 0), fill=tk.X, expand=True)
        
        tk.Label(simple_title_frame, text="简化版界面", 
                font=('Microsoft YaHei UI', 16, 'bold'),
                fg='#1e293b', bg='white').pack(anchor=tk.W)
        
        tk.Label(simple_title_frame, text="适合日常使用，界面简洁，操作便捷", 
                font=('Microsoft YaHei UI', 11),
                fg='#64748b', bg='white').pack(anchor=tk.W, pady=(2, 0))
        
        # 简化版按钮
        simple_btn = self.create_gradient_button(simple_content, 
                                                "🚀 启动简化版", 
                                                self.launch_simple)
        simple_btn.pack(pady=(10, 0))
        
        # 完整版卡片
        full_card = self.create_modern_card(cards_container)
        full_card.pack(fill=tk.X, pady=(0, 20))
        
        full_content = tk.Frame(full_card, bg='white')
        full_content.pack(pady=25, padx=30)
        
        # 完整版图标和标题
        full_header = tk.Frame(full_content, bg='white')
        full_header.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(full_header, text="⚙️", 
                font=('Microsoft YaHei UI', 32),
                bg='white').pack(side=tk.LEFT)
        
        full_title_frame = tk.Frame(full_header, bg='white')
        full_title_frame.pack(side=tk.LEFT, padx=(15, 0), fill=tk.X, expand=True)
        
        tk.Label(full_title_frame, text="完整版界面", 
                font=('Microsoft YaHei UI', 16, 'bold'),
                fg='#1e293b', bg='white').pack(anchor=tk.W)
        
        tk.Label(full_title_frame, text="功能完整，支持高级设置和详细配置", 
                font=('Microsoft YaHei UI', 11),
                fg='#64748b', bg='white').pack(anchor=tk.W, pady=(2, 0))
        
        # 完整版按钮
        full_btn = self.create_gradient_button(full_content, 
                                              "🔧 启动完整版", 
                                              self.launch_full,
                                              bg_color='#10b981',
                                              hover_color='#059669')
        full_btn.pack(pady=(10, 0))
        
        # 底部信息
        footer_frame = tk.Frame(main_container, bg='#f8fafc')
        footer_frame.pack(fill=tk.X, pady=(20, 0))
        
        tk.Label(footer_frame, text="💡 提示：首次使用建议选择简化版", 
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='#f8fafc').pack()
    
    def launch_simple(self):
        """启动简化版"""
        try:
            script_path = Path(__file__).parent / "简化版界面.py"
            if script_path.exists():
                subprocess.Popen([sys.executable, str(script_path)])
                self.root.destroy()
            else:
                messagebox.showerror("错误", "找不到简化版界面文件")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {str(e)}")
    
    def launch_full(self):
        """启动完整版"""
        try:
            script_path = Path(__file__).parent / "编号生成器界面.py"
            if script_path.exists():
                subprocess.Popen([sys.executable, str(script_path)])
                self.root.destroy()
            else:
                messagebox.showerror("错误", "找不到完整版界面文件")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {str(e)}")
    
    def run(self):
        """运行启动器"""
        self.root.mainloop()

if __name__ == "__main__":
    launcher = ModernLauncher()
    launcher.run()
