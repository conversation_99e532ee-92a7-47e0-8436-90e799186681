import os
import time
import json
import threading
from datetime import datetime
from pathlib import Path
import getpass
import socket

class NumberManager:
    def __init__(self, shared_path="\\\\192.168.2.64\\e"):
        self.shared_path = shared_path
        self.data_dir = os.path.join(shared_path, "编号生成器数据")
        self.current_number_file = os.path.join(self.data_dir, "current_number.txt")
        self.log_file = os.path.join(self.data_dir, "number_log.json")
        self.lock_file = os.path.join(self.data_dir, "lock.tmp")
        self.config_file = os.path.join(self.data_dir, "config.json")
        
        # 获取用户信息
        self.username = getpass.getuser()
        self.hostname = socket.gethostname()
        
        # 确保数据目录存在
        self._ensure_data_directory()
        
        # 初始化配置
        self._load_config()
    
    def _ensure_data_directory(self):
        """确保数据目录存在"""
        try:
            os.makedirs(self.data_dir, exist_ok=True)
        except Exception as e:
            raise Exception(f"无法创建数据目录 {self.data_dir}: {str(e)}")
    
    def _load_config(self):
        """加载配置文件"""
        default_config = {
            "start_number": 1001,
            "initialized": False
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = default_config
                self._save_config()
        except Exception:
            self.config = default_config
    
    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise Exception(f"保存配置失败: {str(e)}")
    
    def _acquire_lock(self, timeout=10):
        """获取文件锁"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 检查锁文件是否存在且过期
                if os.path.exists(self.lock_file):
                    try:
                        # 检查锁文件的修改时间，如果超过30秒就认为是死锁
                        lock_time = os.path.getmtime(self.lock_file)
                        if time.time() - lock_time > 30:
                            print(f"检测到过期锁文件，正在清理...")
                            os.remove(self.lock_file)
                    except Exception:
                        pass

                # 尝试创建锁文件
                with open(self.lock_file, 'x') as f:
                    f.write(f"{self.username}@{self.hostname}:{datetime.now().isoformat()}")
                return True
            except FileExistsError:
                # 锁文件已存在，等待一小段时间
                time.sleep(0.1)
            except Exception as e:
                print(f"获取锁时出错: {str(e)}")
                time.sleep(0.1)
        return False
    
    def _release_lock(self):
        """释放文件锁"""
        try:
            if os.path.exists(self.lock_file):
                os.remove(self.lock_file)
        except Exception:
            pass
    
    def _get_current_number(self):
        """获取当前编号"""
        try:
            if os.path.exists(self.current_number_file):
                with open(self.current_number_file, 'r') as f:
                    return int(f.read().strip())
            else:
                # 首次使用，返回起始编号-1
                return self.config["start_number"] - 1
        except Exception:
            return self.config["start_number"] - 1
    
    def _set_current_number(self, number):
        """设置当前编号"""
        with open(self.current_number_file, 'w') as f:
            f.write(str(number))
    
    def _log_number_allocation(self, number, count=1):
        """记录编号分配日志"""
        log_entry = {
            "number": number,
            "count": count,
            "user": self.username,
            "hostname": self.hostname,
            "timestamp": datetime.now().isoformat(),
            "date": datetime.now().strftime("%Y-%m-%d"),
            "time": datetime.now().strftime("%H:%M:%S")
        }
        
        try:
            # 读取现有日志
            logs = []
            if os.path.exists(self.log_file):
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            
            # 添加新日志
            logs.append(log_entry)
            
            # 只保留最近1000条记录
            if len(logs) > 1000:
                logs = logs[-1000:]
            
            # 保存日志
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"记录日志失败: {str(e)}")
    
    def get_next_number(self, count=1):
        """获取下一个（或多个）编号"""
        if not self._acquire_lock():
            raise Exception("获取锁超时，请稍后重试")
        
        try:
            current = self._get_current_number()
            next_number = current + 1
            new_current = current + count
            
            self._set_current_number(new_current)
            self._log_number_allocation(next_number, count)
            
            if count == 1:
                return next_number
            else:
                return list(range(next_number, next_number + count))
                
        finally:
            self._release_lock()
    
    def get_recent_logs(self, limit=20):
        """获取最近的编号分配记录"""
        try:
            if not os.path.exists(self.log_file):
                return []
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
            
            return logs[-limit:] if logs else []
        except Exception:
            return []
    
    def get_current_status(self):
        """获取当前状态"""
        try:
            current = self._get_current_number()
            recent_logs = self.get_recent_logs(5)
            
            return {
                "current_number": current,
                "next_number": current + 1,
                "recent_logs": recent_logs,
                "shared_path": self.shared_path,
                "data_dir_exists": os.path.exists(self.data_dir)
            }
        except Exception as e:
            return {
                "error": str(e),
                "shared_path": self.shared_path,
                "data_dir_exists": False
            }
    
    def set_start_number(self, start_number):
        """设置起始编号（仅在未初始化时可用）"""
        if self.config.get("initialized", False):
            raise Exception("系统已初始化，无法修改起始编号")
        
        if not self._acquire_lock():
            raise Exception("获取锁超时，请稍后重试")
        
        try:
            self.config["start_number"] = start_number
            self.config["initialized"] = True
            self._save_config()
            
            # 设置当前编号为起始编号-1
            self._set_current_number(start_number - 1)
            
        finally:
            self._release_lock()
    
    def reset_system(self, new_start_number):
        """重置系统（危险操作，会清除所有数据）"""
        if not self._acquire_lock():
            raise Exception("获取锁超时，请稍后重试")
        
        try:
            # 备份现有日志
            if os.path.exists(self.log_file):
                backup_file = os.path.join(self.data_dir, f"number_log_backup_{int(time.time())}.json")
                os.rename(self.log_file, backup_file)
            
            # 重置配置
            self.config = {
                "start_number": new_start_number,
                "initialized": True
            }
            self._save_config()
            
            # 重置当前编号
            self._set_current_number(new_start_number - 1)
            
            # 记录重置操作
            self._log_number_allocation(new_start_number - 1, 0)  # count=0表示重置操作
            
        finally:
            self._release_lock()
