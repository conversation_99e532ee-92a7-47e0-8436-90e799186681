# 互感器评审编号生成器 v1.0

## 项目简介

这是一个专为三人团队设计的互感器评审编号生成器，解决多人同时工作时编号分配冲突的问题。程序采用共享文件 + 文件锁机制，确保编号的连续性和唯一性。

## 主要特性

- ✅ **连续编号分配** - 确保编号连续，无重复
- ✅ **多人同时使用** - 支持3人同时获取编号，自动排队处理
- ✅ **局域网共享** - 基于网络共享文件夹，无需专门服务器
- ✅ **实时状态显示** - 显示当前编号状态和最近分配记录
- ✅ **冒泡消息提醒** - 编号分配成功后显示通知消息
- ✅ **批量编号获取** - 支持一次获取多个连续编号
- ✅ **操作历史记录** - 记录谁在什么时间获取了哪些编号
- ✅ **绿色免安装** - 打包成exe文件，双击即用

## 文件结构

```
编号生成器1.0/
├── 编号生成器.py          # 主启动程序
├── main_gui.py            # GUI界面模块
├── number_manager.py      # 核心编号管理模块
├── build_exe.py           # 打包脚本
├── test_number_manager.py # 测试脚本
├── requirements.txt       # 依赖说明
└── README.md             # 项目说明
```

## 快速开始

### 方法一：直接运行Python程序

1. 确保安装了Python 3.6+
2. 双击运行 `编号生成器.py`

### 方法二：打包成exe文件

1. 运行打包脚本：
   ```bash
   python build_exe.py
   ```

2. 在 `release/` 文件夹中找到 `编号生成器.exe`

3. 将整个 `release/` 文件夹分发给团队成员

## 使用说明

### 首次使用

1. **启动程序** - 双击 `编号生成器.exe`
2. **设置起始编号** - 点击"设置"按钮，输入起始编号（如1001）
3. **确认共享路径** - 程序默认使用 `\\192.168.2.64\e` 作为共享路径

### 日常使用

1. **获取单个编号** - 点击"获取下一个编号"按钮
2. **批量获取编号** - 点击"批量获取编号"，输入需要的数量
3. **查看历史记录** - 在下方表格中查看最近的编号分配记录
4. **刷新状态** - 点击"刷新状态"获取最新信息

### 界面说明

- **当前状态区域** - 显示下一个可用编号和共享路径
- **操作按钮区域** - 提供编号获取和设置功能
- **历史记录表格** - 显示最近的编号分配记录
- **消息提示区域** - 显示操作结果和状态信息
- **冒泡通知窗口** - 编号分配成功后的弹出提醒

## 技术原理

### 文件锁机制

程序使用文件锁确保同时只有一个用户能修改编号：

1. 用户点击获取编号
2. 程序尝试创建锁文件
3. 读取当前编号，+1后写回
4. 删除锁文件，释放锁定
5. 整个过程通常在0.1秒内完成

### 数据存储

所有数据存储在共享路径的 `编号生成器数据/` 文件夹中：

- `current_number.txt` - 当前最大编号
- `number_log.json` - 编号分配历史记录
- `config.json` - 系统配置信息
- `lock.tmp` - 临时锁文件

### 错误处理

- **网络中断** - 显示连接失败，可重试
- **文件锁超时** - 10秒超时，提示稍后重试
- **权限问题** - 提示检查共享文件夹权限
- **数据损坏** - 自动备份和恢复机制

## 测试

运行测试脚本验证功能：

```bash
python test_number_manager.py
```

测试内容包括：
- 基本功能测试
- 并发访问测试
- 网络路径测试

## 配置说明

### 修改共享路径

如需修改共享路径，编辑 `number_manager.py` 文件第8行：

```python
def __init__(self, shared_path="\\\\你的服务器IP\\共享文件夹"):
```

### 修改起始编号

首次使用时通过程序界面设置，或直接修改配置文件。

## 常见问题

### Q: 提示"无法访问共享路径"
A: 检查网络连接和共享文件夹权限，确保所有用户都能读写该路径。

### Q: 编号出现重复
A: 这种情况极少发生，如果出现请重启程序并检查网络连接。

### Q: 程序启动失败
A: 确保Python环境正确，或使用打包好的exe文件。

### Q: 多人同时使用时很慢
A: 正常现象，程序会自动排队处理，通常延迟不超过1秒。

## 系统要求

- **操作系统**: Windows 7/10/11
- **Python版本**: 3.6+ (如使用源码)
- **网络要求**: 能访问共享文件夹
- **权限要求**: 对共享文件夹有读写权限

## 界面展示

### 现代化设计特点
- 🎨 **扁平化设计风格** - 采用Material Design设计理念
- 📱 **大号编号显示** - 28号字体，醒目的蓝色突出显示
- 🎯 **卡片式布局** - 清晰的层次结构，美观的阴影效果
- 🌈 **协调配色方案** - 专业的色彩搭配，视觉舒适
- ✨ **现代化交互** - 按钮悬停效果，动画消息提醒
- 📐 **响应式布局** - 适配不同屏幕尺寸

### 界面预览
运行以下命令查看界面设计说明和预览：
```bash
python 界面预览.py
```

## 更新日志

### v1.1 (2024-08-02) - 界面现代化更新
- 🎨 **全新现代化界面设计**
  - 采用扁平化设计风格和Material Design配色
  - 编号显示字体增大到28号，更加醒目
  - 使用卡片式布局，层次分明
  - 添加渐变背景和装饰元素
- ✨ **交互体验优化**
  - 按钮悬停效果和动画反馈
  - 现代化冒泡通知消息
  - 消息动画效果
  - 改进的设置对话框
- 🎯 **视觉优化**
  - 协调的配色方案
  - Microsoft YaHei UI字体
  - 合理的间距和边距
  - 清晰的视觉层次

### v1.0 (2024-08-02)
- ✅ 初始版本发布
- ✅ 基本编号分配功能
- ✅ GUI界面
- ✅ 多人并发支持
- ✅ 历史记录功能
- ✅ 冒泡消息提醒

## 技术支持

如遇问题请联系开发团队或查看：
- 测试脚本输出
- 程序错误提示
- 共享文件夹访问权限

---

**开发信息**
- 版本: 1.0
- 开发语言: Python 3
- GUI框架: tkinter
- 打包工具: PyInstaller
