#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互感器评审编号生成器
版本: 1.0
作者: AI助手
功能: 为三人团队提供连续编号分配，支持局域网共享
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

def main():
    """主函数"""
    try:
        # 尝试导入现代化界面，如果失败则使用简化版
        try:
            from main_gui import NumberGeneratorGUI
            app = NumberGeneratorGUI()
        except Exception:
            # 如果现代化界面有问题，使用简化版
            from 简化版界面 import SimpleModernGUI
            app = SimpleModernGUI()

        # 运行应用
        app.run()
        
    except ImportError as e:
        error_msg = f"导入模块失败: {str(e)}\n请确保所有文件都在同一目录下。"
        if 'tkinter' in str(e):
            error_msg += "\n\n可能需要安装tkinter模块。"
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
        except:
            print(error_msg)
        
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
        except:
            print(error_msg)
        
        sys.exit(1)

if __name__ == "__main__":
    main()
