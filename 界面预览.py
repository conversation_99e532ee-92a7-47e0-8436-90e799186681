#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编号生成器界面预览
用于展示新的现代化界面设计
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def preview_interface():
    """预览界面"""
    try:
        # 导入主程序
        from main_gui import NumberGeneratorGUI
        
        # 创建预览窗口
        preview_root = tk.Tk()
        preview_root.withdraw()  # 隐藏主窗口
        
        # 显示预览说明
        result = messagebox.askyesno(
            "界面预览", 
            "即将启动编号生成器的新界面预览。\n\n"
            "新界面特点：\n"
            "✅ 现代化扁平设计风格\n"
            "✅ 大号编号显示，更加醒目\n"
            "✅ 卡片式布局，层次分明\n"
            "✅ 现代化按钮和悬停效果\n"
            "✅ 美观的冒泡通知消息\n"
            "✅ 协调的配色方案\n\n"
            "是否继续启动预览？"
        )
        
        preview_root.destroy()
        
        if result:
            # 启动主程序
            app = NumberGeneratorGUI()
            
            # 显示欢迎消息
            app.root.after(1000, lambda: app.show_message("欢迎使用现代化编号生成器！", "success"))
            app.root.after(3000, lambda: app.show_bubble_message("界面预览模式"))
            
            app.run()
        else:
            print("用户取消预览")
            
    except ImportError as e:
        error_msg = f"导入失败: {str(e)}\n请确保所有文件都在同一目录下。"
        messagebox.showerror("错误", error_msg)
        
    except Exception as e:
        error_msg = f"启动失败: {str(e)}"
        messagebox.showerror("错误", error_msg)

def show_design_info():
    """显示设计说明"""
    info_window = tk.Tk()
    info_window.title("界面设计说明")
    info_window.geometry("600x500")
    info_window.configure(bg='#f5f5f5')
    
    # 主容器
    main_frame = tk.Frame(info_window, bg='#f5f5f5')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 标题
    title_label = tk.Label(main_frame, text="🎨 现代化界面设计说明", 
                          font=('Microsoft YaHei UI', 16, 'bold'),
                          fg='#2c3e50', bg='#f5f5f5')
    title_label.pack(pady=(0, 20))
    
    # 设计特点
    features_text = """
📋 界面现代化改进：

1. 设计风格
   • 采用扁平化设计理念
   • 使用Material Design配色方案
   • 卡片式布局，层次分明
   • 适当的阴影和圆角效果

2. 编号显示优化
   • 编号字体大小增加到28号
   • 使用醒目的蓝色突出显示
   • 添加背景色块和边距
   • 放置在界面显眼位置

3. 配色方案
   • 主色调：#3498db (蓝色)
   • 成功色：#27ae60 (绿色)
   • 警告色：#f39c12 (橙色)
   • 错误色：#e74c3c (红色)
   • 背景色：#f5f5f5 (浅灰)

4. 交互体验
   • 按钮悬停效果
   • 消息动画效果
   • 现代化冒泡通知
   • 渐变背景装饰

5. 字体优化
   • 使用Microsoft YaHei UI字体
   • 不同层级使用不同字重
   • 合理的字号搭配

6. 布局改进
   • 增加间距和边距
   • 使用网格和卡片布局
   • 响应式设计适配
   • 清晰的视觉层次
"""
    
    # 创建文本显示区域
    text_frame = tk.Frame(main_frame, bg='white', relief='flat', bd=1)
    text_frame.pack(fill=tk.BOTH, expand=True)
    
    text_widget = tk.Text(text_frame, 
                         font=('Microsoft YaHei UI', 10),
                         bg='white', fg='#2c3e50',
                         relief='flat', bd=0,
                         padx=20, pady=20,
                         wrap=tk.WORD)
    text_widget.pack(fill=tk.BOTH, expand=True)
    text_widget.insert(tk.END, features_text)
    text_widget.configure(state='disabled')
    
    # 按钮区域
    button_frame = tk.Frame(main_frame, bg='#f5f5f5')
    button_frame.pack(fill=tk.X, pady=(20, 0))
    
    # 预览按钮
    preview_btn = tk.Button(button_frame, text="🚀 启动界面预览",
                           command=lambda: [info_window.destroy(), preview_interface()],
                           font=('Microsoft YaHei UI', 11, 'bold'),
                           bg='#3498db', fg='white',
                           relief='flat', bd=0,
                           padx=20, pady=10,
                           cursor='hand2')
    preview_btn.pack(side=tk.LEFT, padx=(0, 10))
    
    # 关闭按钮
    close_btn = tk.Button(button_frame, text="关闭",
                         command=info_window.destroy,
                         font=('Microsoft YaHei UI', 10),
                         bg='white', fg='#34495e',
                         relief='flat', bd=1,
                         padx=20, pady=8,
                         cursor='hand2')
    close_btn.pack(side=tk.RIGHT)
    
    info_window.mainloop()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--preview":
        preview_interface()
    else:
        show_design_info()
