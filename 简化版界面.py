#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编号生成器 - 简化版现代化界面
确保稳定性和兼容性
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import time
from datetime import datetime
from number_manager import NumberManager

class SimpleModernGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("互感器评审编号生成器 - 简化版")
        self.root.geometry("750x750")  # 增加窗口高度以显示完整界面
        self.root.configure(bg='#f8fafc')
        self.root.resizable(True, True)  # 允许用户调整窗口大小
        
        # 初始化编号管理器
        try:
            self.manager = NumberManager()
        except Exception as e:
            messagebox.showerror("错误", f"初始化失败: {str(e)}")
            self.root.destroy()
            return
        
        # 创建界面
        self.create_widgets()
        
        # 启动状态更新线程
        self.running = True
        self.update_thread = threading.Thread(target=self.update_status_loop, daemon=True)
        self.update_thread.start()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_modern_card(self, parent):
        """创建现代化卡片样式"""
        card = tk.Frame(parent, bg='white', relief='flat', bd=0)
        # 添加阴影效果（通过边框模拟）
        shadow = tk.Frame(parent, bg='#e2e8f0', height=2)
        return card

    def create_primary_button(self, parent, text, command):
        """创建主要操作按钮"""
        button = tk.Button(parent, text=text,
                          command=command,
                          font=('Microsoft YaHei UI', 14, 'bold'),
                          bg='#3b82f6', fg='white',
                          relief='flat', bd=0,
                          padx=40, pady=15,
                          cursor='hand2')
        # 添加悬停效果
        button.bind("<Enter>", lambda e: button.config(bg='#2563eb'))
        button.bind("<Leave>", lambda e: button.config(bg='#3b82f6'))
        return button

    def create_secondary_button(self, parent, text, command):
        """创建次要操作按钮"""
        button = tk.Button(parent, text=text,
                          command=command,
                          font=('Microsoft YaHei UI', 11),
                          bg='#f1f5f9', fg='#475569',
                          relief='flat', bd=0,
                          padx=25, pady=10,
                          cursor='hand2')
        # 添加悬停效果
        button.bind("<Enter>", lambda e: button.config(bg='#e2e8f0'))
        button.bind("<Leave>", lambda e: button.config(bg='#f1f5f9'))
        return button

    def create_widgets(self):
        """创建精美的现代化界面"""
        # 设置渐变背景色
        self.root.configure(bg='#f8fafc')

        # 主容器
        main_frame = tk.Frame(self.root, bg='#f8fafc')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=25)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#f8fafc')
        title_frame.pack(fill=tk.X, pady=(0, 35))

        title_label = tk.Label(title_frame, text="互感器评审编号生成器",
                              font=('Microsoft YaHei UI', 20, 'bold'),
                              fg='#1e293b', bg='#f8fafc')
        title_label.pack()

        subtitle_label = tk.Label(title_frame, text="智能分配 • 多人协作 • 实时同步",
                                 font=('Microsoft YaHei UI', 11),
                                 fg='#64748b', bg='#f8fafc')
        subtitle_label.pack(pady=(8, 0))
        
        # 编号显示卡片 - 精美设计
        number_card = self.create_modern_card(main_frame)
        number_card.pack(fill=tk.X, pady=(0, 25))

        # 编号显示内容
        number_content = tk.Frame(number_card, bg='white')
        number_content.pack(pady=30, padx=30)

        # 卡片标题
        tk.Label(number_content, text="🎯 编号获取中心",
                font=('Microsoft YaHei UI', 14, 'bold'),
                fg='#1e293b', bg='white').pack()

        tk.Label(number_content, text="点击下方按钮获取您的专属编号",
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='white').pack(pady=(5, 20))

        # 编号显示区域（初始隐藏）
        self.number_display_container = tk.Frame(number_content, bg='white')
        # 不立即pack，等用户点击按钮后再显示

        # 编号显示背景
        number_bg = tk.Frame(self.number_display_container, bg='#dbeafe', relief='flat', bd=0)
        number_bg.pack(padx=20, pady=10)

        # 编号标签
        self.next_number_var = tk.StringVar(value="")
        self.number_label = tk.Label(number_bg, textvariable=self.next_number_var,
                                    font=('Microsoft YaHei UI', 32, 'bold'),
                                    fg='#1d4ed8', bg='#dbeafe',
                                    padx=40, pady=20)
        self.number_label.pack()

        # 等待提示区域
        self.waiting_frame = tk.Frame(number_content, bg='white')
        self.waiting_frame.pack(pady=(0, 20))

        tk.Label(self.waiting_frame, text="🔄",
                font=('Microsoft YaHei UI', 24),
                fg='#64748b', bg='white').pack()

        tk.Label(self.waiting_frame, text="等待获取编号...",
                font=('Microsoft YaHei UI', 12),
                fg='#64748b', bg='white').pack(pady=(5, 0))

        # 状态信息
        status_frame = tk.Frame(number_content, bg='white')
        status_frame.pack()

        status_icon = tk.Label(status_frame, text="🌐",
                              font=('Microsoft YaHei UI', 12),
                              bg='white')
        status_icon.pack(side=tk.LEFT)

        tk.Label(status_frame, text="共享路径: ",
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='white').pack(side=tk.LEFT, padx=(5, 0))

        self.shared_path_var = tk.StringVar()
        tk.Label(status_frame, textvariable=self.shared_path_var,
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='white').pack(side=tk.LEFT)
        
        # 操作按钮区域 - 精美卡片设计
        button_card = self.create_modern_card(main_frame)
        button_card.pack(fill=tk.X, pady=(0, 25))

        button_content = tk.Frame(button_card, bg='white')
        button_content.pack(pady=25, padx=30)

        # 操作说明
        instruction_frame = tk.Frame(button_content, bg='white')
        instruction_frame.pack(pady=(0, 20))

        tk.Label(instruction_frame, text="💡 操作指南",
                font=('Microsoft YaHei UI', 12, 'bold'),
                fg='#1e293b', bg='white').pack()

        instruction_text = tk.Frame(instruction_frame, bg='white')
        instruction_text.pack(pady=(8, 0))

        tk.Label(instruction_text, text="1️⃣ 点击'获取我的编号'按钮",
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='white').pack(anchor=tk.W)

        tk.Label(instruction_text, text="2️⃣ 系统自动分配专属编号给您",
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='white').pack(anchor=tk.W, pady=(2, 0))

        tk.Label(instruction_text, text="3️⃣ 编号会显示在上方并自动记录",
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='white').pack(anchor=tk.W, pady=(2, 0))

        # 主要操作按钮
        main_button_frame = tk.Frame(button_content, bg='white')
        main_button_frame.pack(pady=(0, 20))

        # 获取单个编号按钮
        self.get_number_btn = self.create_primary_button(main_button_frame,
                                                        "🎯 获取我的编号",
                                                        self.get_single_number)
        self.get_number_btn.pack(pady=(0, 10))

        # 批量获取按钮
        self.get_batch_btn = tk.Button(main_button_frame, text="📊 批量获取编号",
                                      command=self.get_batch_numbers,
                                      font=('Microsoft YaHei UI', 12),
                                      bg='#10b981', fg='white',
                                      relief='flat', bd=0,
                                      padx=35, pady=12,
                                      cursor='hand2')
        self.get_batch_btn.bind("<Enter>", lambda e: self.get_batch_btn.config(bg='#059669'))
        self.get_batch_btn.bind("<Leave>", lambda e: self.get_batch_btn.config(bg='#10b981'))
        self.get_batch_btn.pack()

        # 次要操作按钮
        secondary_frame = tk.Frame(button_content, bg='white')
        secondary_frame.pack()

        refresh_btn = self.create_secondary_button(secondary_frame,
                                                  "🔄 刷新状态",
                                                  self.refresh_status)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 15))

        settings_btn = self.create_secondary_button(secondary_frame,
                                                   "⚙️ 系统设置",
                                                   self.show_settings)
        settings_btn.pack(side=tk.LEFT)
        
        # 历史记录区域 - 精美设计
        history_card = self.create_modern_card(main_frame)
        history_card.pack(fill=tk.BOTH, expand=True)

        # 历史记录标题
        history_header = tk.Frame(history_card, bg='white')
        history_header.pack(fill=tk.X, padx=25, pady=(20, 10))

        tk.Label(history_header, text="📊 编号分配记录",
                font=('Microsoft YaHei UI', 14, 'bold'),
                fg='#1e293b', bg='white').pack(side=tk.LEFT)

        tk.Label(history_header, text="实时显示所有用户的编号获取情况",
                font=('Microsoft YaHei UI', 10),
                fg='#64748b', bg='white').pack(side=tk.LEFT, padx=(15, 0))

        # 表格容器
        table_container = tk.Frame(history_card, bg='white')
        table_container.pack(fill=tk.BOTH, expand=True, padx=25, pady=(0, 20))

        # 创建表格样式
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Custom.Treeview",
                       background="white",
                       foreground="#1e293b",
                       rowheight=30,
                       fieldbackground="white",
                       font=('Microsoft YaHei UI', 10))
        style.configure("Custom.Treeview.Heading",
                       background="#f8fafc",
                       foreground="#475569",
                       font=('Microsoft YaHei UI', 10, 'bold'))

        columns = ("编号", "数量", "用户", "计算机", "日期", "时间")
        self.history_tree = ttk.Treeview(table_container, columns=columns,
                                        show="headings", height=8,
                                        style="Custom.Treeview")

        # 设置列宽和对齐
        column_configs = {
            "编号": (90, tk.CENTER),
            "数量": (70, tk.CENTER),
            "用户": (110, tk.CENTER),
            "计算机": (130, tk.CENTER),
            "日期": (110, tk.CENTER),
            "时间": (90, tk.CENTER)
        }

        for col in columns:
            width, anchor = column_configs[col]
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=width, anchor=anchor)

        # 滚动条
        scrollbar = ttk.Scrollbar(table_container, orient=tk.VERTICAL,
                                 command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)

        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 状态消息区域
        self.message_var = tk.StringVar()
        self.message_label = tk.Label(main_frame, textvariable=self.message_var,
                                     font=('Microsoft YaHei UI', 11),
                                     fg='#059669', bg='#f8fafc',
                                     pady=8)
        self.message_label.pack(pady=(15, 0))
        
        # 初始化状态（不自动显示编号）
        # self.refresh_status()  # 注释掉，避免初始显示编号

        # 只加载历史记录
        self.load_history()
    
    def get_single_number(self):
        """获取单个编号"""
        try:
            self.get_number_btn.config(state="disabled")

            # 隐藏等待提示，显示编号区域
            self.waiting_frame.pack_forget()
            self.number_display_container.pack(pady=(0, 20))

            # 获取编号
            number = self.manager.get_next_number()

            # 更新编号显示
            self.next_number_var.set(number)

            self.show_message(f"🎉 编号获取成功！您的专属编号是: {number}")
            self.show_bubble_message(f"🎯 您的编号: {number}")
            self.refresh_status()

        except Exception as e:
            # 如果出错，重新显示等待提示
            self.number_display_container.pack_forget()
            self.waiting_frame.pack(pady=(0, 20))
            messagebox.showerror("错误", f"获取编号失败: {str(e)}")
        finally:
            self.get_number_btn.config(state="normal")
    
    def get_batch_numbers(self):
        """批量获取编号"""
        try:
            count = simpledialog.askinteger("批量获取", "请输入要获取的编号数量:", 
                                          minvalue=1, maxvalue=100)
            if count is None:
                return
            
            self.get_batch_btn.config(state="disabled")
            numbers = self.manager.get_next_number(count)
            
            if count <= 10:
                numbers_str = ", ".join(map(str, numbers))
            else:
                numbers_str = f"{numbers[0]} ~ {numbers[-1]} (共{count}个)"
            
            self.show_message(f"✅ 成功获取编号: {numbers_str}")
            self.show_bubble_message(f"批量获取: {numbers_str}")
            self.refresh_status()
            
        except Exception as e:
            messagebox.showerror("错误", f"批量获取编号失败: {str(e)}")
        finally:
            self.get_batch_btn.config(state="normal")
    
    def load_history(self):
        """只加载历史记录，不显示编号"""
        try:
            status = self.manager.get_current_status()

            if "error" in status:
                self.shared_path_var.set(f"错误: {status['error']}")
                return

            # 只设置共享路径，不设置编号
            self.shared_path_var.set(status["shared_path"])

            # 更新历史记录
            self.update_history_table(status["recent_logs"])

        except Exception as e:
            self.shared_path_var.set(f"加载失败: {str(e)}")

    def refresh_status(self):
        """刷新状态"""
        try:
            status = self.manager.get_current_status()

            if "error" in status:
                self.next_number_var.set("连接失败")
                self.shared_path_var.set(f"错误: {status['error']}")
                return

            self.next_number_var.set(str(status["next_number"]))
            self.shared_path_var.set(status["shared_path"])

            # 更新历史记录
            self.update_history_table(status["recent_logs"])

        except Exception as e:
            self.next_number_var.set("错误")
            self.shared_path_var.set(f"刷新失败: {str(e)}")
    
    def update_history_table(self, logs):
        """更新历史记录表格"""
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        for log in reversed(logs):
            if log.get("count", 1) == 0:
                continue
                
            count = log.get("count", 1)
            if count == 1:
                number_display = str(log["number"])
            else:
                end_number = log["number"] + count - 1
                number_display = f"{log['number']}~{end_number}"
            
            self.history_tree.insert("", 0, values=(
                number_display, count, log["user"], log["hostname"], log["date"], log["time"]
            ))
    
    def show_message(self, message):
        """显示消息"""
        self.message_var.set(message)
        self.root.after(3000, lambda: self.message_var.set(""))
    
    def show_bubble_message(self, message):
        """显示简化版冒泡消息"""
        bubble = tk.Toplevel(self.root)
        bubble.title("通知")
        bubble.geometry("300x80")
        bubble.configure(bg='#3498db')
        bubble.resizable(False, False)
        
        x = self.root.winfo_x() + self.root.winfo_width() - 320
        y = self.root.winfo_y() + self.root.winfo_height() - 100
        bubble.geometry(f"300x80+{x}+{y}")
        
        bubble.attributes("-topmost", True)
        
        tk.Label(bubble, text="🎉 编号分配成功", 
                font=('Microsoft YaHei UI', 10, 'bold'),
                fg='white', bg='#3498db').pack(pady=(10, 5))
        
        tk.Label(bubble, text=message,
                font=('Microsoft YaHei UI', 9),
                fg='white', bg='#3498db').pack()
        
        bubble.after(2000, bubble.destroy)
    
    def show_settings(self):
        """显示设置对话框"""
        settings = tk.Toplevel(self.root)
        settings.title("设置")
        settings.geometry("350x200")
        settings.configure(bg='#f5f5f5')
        settings.resizable(False, False)
        
        settings.transient(self.root)
        settings.grab_set()
        
        frame = tk.Frame(settings, bg='#f5f5f5')
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        tk.Label(frame, text="⚙️ 系统设置", 
                font=('Microsoft YaHei UI', 14, 'bold'),
                fg='#2c3e50', bg='#f5f5f5').pack(pady=(0, 20))
        
        if not self.manager.config.get("initialized", False):
            tk.Label(frame, text="设置起始编号:", 
                    font=('Microsoft YaHei UI', 10),
                    fg='#2c3e50', bg='#f5f5f5').pack()
            
            start_number_var = tk.StringVar(value=str(self.manager.config["start_number"]))
            entry = tk.Entry(frame, textvariable=start_number_var, 
                           font=('Microsoft YaHei UI', 11), width=20)
            entry.pack(pady=(5, 15))
            
            def set_start():
                try:
                    start_num = int(start_number_var.get())
                    self.manager.set_start_number(start_num)
                    messagebox.showinfo("成功", f"起始编号已设置为: {start_num}")
                    settings.destroy()
                    self.refresh_status()
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的数字")
                except Exception as e:
                    messagebox.showerror("错误", str(e))
            
            tk.Button(frame, text="设置", command=set_start,
                     font=('Microsoft YaHei UI', 10),
                     bg='#3498db', fg='white',
                     relief='flat', padx=20, pady=8).pack(pady=(0, 10))
        else:
            tk.Label(frame, text=f"当前起始编号: {self.manager.config['start_number']}", 
                    font=('Microsoft YaHei UI', 11),
                    fg='#2c3e50', bg='#f5f5f5').pack(pady=(0, 10))
            tk.Label(frame, text="系统已初始化", 
                    font=('Microsoft YaHei UI', 10),
                    fg='#7f8c8d', bg='#f5f5f5').pack()
        
        tk.Button(frame, text="关闭", command=settings.destroy,
                 font=('Microsoft YaHei UI', 10),
                 bg='#ecf0f1', fg='#2c3e50',
                 relief='flat', padx=20, pady=8).pack(side=tk.BOTTOM)
    
    def update_status_loop(self):
        """状态更新循环"""
        while self.running:
            try:
                time.sleep(5)
                if self.running:
                    self.root.after(0, self.refresh_status)
            except Exception:
                break
    
    def on_closing(self):
        """关闭程序"""
        self.running = False
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleModernGUI()
    app.run()
