#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编号生成器 - 简化版现代化界面
确保稳定性和兼容性
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import time
from datetime import datetime
from number_manager import NumberManager

class SimpleModernGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("编号生成器 v1.1")
        self.root.geometry("800x600")
        self.root.configure(bg='#f5f5f5')
        
        # 初始化编号管理器
        try:
            self.manager = NumberManager()
        except Exception as e:
            messagebox.showerror("错误", f"初始化失败: {str(e)}")
            self.root.destroy()
            return
        
        # 创建界面
        self.create_widgets()
        
        # 启动状态更新线程
        self.running = True
        self.update_thread = threading.Thread(target=self.update_status_loop, daemon=True)
        self.update_thread.start()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建简化版现代化界面"""
        # 主容器
        main_frame = tk.Frame(self.root, bg='#f5f5f5')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#f5f5f5')
        title_frame.pack(fill=tk.X, pady=(0, 30))
        
        title_label = tk.Label(title_frame, text="互感器评审编号生成器", 
                              font=('Microsoft YaHei UI', 18, 'bold'),
                              fg='#2c3e50', bg='#f5f5f5')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="多人协作 • 实时同步 • 安全可靠", 
                                 font=('Microsoft YaHei UI', 10),
                                 fg='#7f8c8d', bg='#f5f5f5')
        subtitle_label.pack(pady=(5, 0))
        
        # 编号显示卡片
        number_card = tk.Frame(main_frame, bg='white', relief='solid', bd=1)
        number_card.pack(fill=tk.X, pady=(0, 20))
        
        # 编号显示内容
        number_content = tk.Frame(number_card, bg='white')
        number_content.pack(pady=20, padx=20)
        
        tk.Label(number_content, text="下一个编号", 
                font=('Microsoft YaHei UI', 12),
                fg='#7f8c8d', bg='white').pack()
        
        # 大号编号显示
        self.next_number_var = tk.StringVar(value="加载中...")
        number_display = tk.Frame(number_content, bg='#e8f4fd', relief='flat')
        number_display.pack(pady=(10, 10), fill=tk.X)
        
        self.number_label = tk.Label(number_display, textvariable=self.next_number_var,
                                    font=('Microsoft YaHei UI', 24, 'bold'),
                                    fg='#2980b9', bg='#e8f4fd', pady=15)
        self.number_label.pack()
        
        # 状态信息
        status_frame = tk.Frame(number_content, bg='white')
        status_frame.pack()
        
        tk.Label(status_frame, text="共享路径: ", 
                font=('Microsoft YaHei UI', 9),
                fg='#7f8c8d', bg='white').pack(side=tk.LEFT)
        
        self.shared_path_var = tk.StringVar()
        tk.Label(status_frame, textvariable=self.shared_path_var,
                font=('Microsoft YaHei UI', 9),
                fg='#7f8c8d', bg='white').pack(side=tk.LEFT)
        
        # 操作按钮区域
        button_card = tk.Frame(main_frame, bg='white', relief='solid', bd=1)
        button_card.pack(fill=tk.X, pady=(0, 20))
        
        button_content = tk.Frame(button_card, bg='white')
        button_content.pack(pady=20, padx=20)
        
        # 主要按钮
        main_buttons = tk.Frame(button_content, bg='white')
        main_buttons.pack(pady=(0, 10))
        
        self.get_number_btn = tk.Button(main_buttons, text="📋 获取下一个编号",
                                       command=self.get_single_number,
                                       font=('Microsoft YaHei UI', 12, 'bold'),
                                       bg='#3498db', fg='white',
                                       relief='flat', bd=0,
                                       padx=25, pady=12)
        self.get_number_btn.pack(side=tk.LEFT, padx=(0, 15))
        
        self.get_batch_btn = tk.Button(main_buttons, text="📊 批量获取",
                                      command=self.get_batch_numbers,
                                      font=('Microsoft YaHei UI', 11),
                                      bg='#2ecc71', fg='white',
                                      relief='flat', bd=0,
                                      padx=20, pady=10)
        self.get_batch_btn.pack(side=tk.LEFT)
        
        # 次要按钮
        secondary_buttons = tk.Frame(button_content, bg='white')
        secondary_buttons.pack()
        
        refresh_btn = tk.Button(secondary_buttons, text="🔄 刷新",
                               command=self.refresh_status,
                               font=('Microsoft YaHei UI', 10),
                               bg='#ecf0f1', fg='#2c3e50',
                               relief='flat', bd=0,
                               padx=15, pady=8)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        settings_btn = tk.Button(secondary_buttons, text="⚙️ 设置",
                                command=self.show_settings,
                                font=('Microsoft YaHei UI', 10),
                                bg='#ecf0f1', fg='#2c3e50',
                                relief='flat', bd=0,
                                padx=15, pady=8)
        settings_btn.pack(side=tk.LEFT)
        
        # 历史记录区域
        history_card = tk.Frame(main_frame, bg='white', relief='solid', bd=1)
        history_card.pack(fill=tk.BOTH, expand=True)
        
        # 历史记录标题
        history_header = tk.Frame(history_card, bg='white')
        history_header.pack(fill=tk.X, padx=15, pady=(15, 5))
        
        tk.Label(history_header, text="📋 最近编号分配记录",
                font=('Microsoft YaHei UI', 12, 'bold'),
                fg='#2c3e50', bg='white').pack(side=tk.LEFT)
        
        # 表格
        table_frame = tk.Frame(history_card, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        columns = ("编号", "数量", "用户", "计算机", "日期", "时间")
        self.history_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            if col == "编号":
                self.history_tree.column(col, width=80, anchor=tk.CENTER)
            elif col == "数量":
                self.history_tree.column(col, width=60, anchor=tk.CENTER)
            elif col == "用户":
                self.history_tree.column(col, width=100, anchor=tk.CENTER)
            elif col == "计算机":
                self.history_tree.column(col, width=120, anchor=tk.CENTER)
            elif col == "日期":
                self.history_tree.column(col, width=100, anchor=tk.CENTER)
            elif col == "时间":
                self.history_tree.column(col, width=80, anchor=tk.CENTER)
        
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 消息区域
        self.message_var = tk.StringVar()
        self.message_label = tk.Label(main_frame, textvariable=self.message_var,
                                     font=('Microsoft YaHei UI', 10),
                                     fg='#27ae60', bg='#f5f5f5')
        self.message_label.pack(pady=(10, 0))
        
        # 初始化状态
        self.refresh_status()
    
    def get_single_number(self):
        """获取单个编号"""
        try:
            self.get_number_btn.config(state="disabled")
            number = self.manager.get_next_number()
            
            self.show_message(f"✅ 成功获取编号: {number}")
            self.show_bubble_message(f"获取编号: {number}")
            self.refresh_status()
            
        except Exception as e:
            messagebox.showerror("错误", f"获取编号失败: {str(e)}")
        finally:
            self.get_number_btn.config(state="normal")
    
    def get_batch_numbers(self):
        """批量获取编号"""
        try:
            count = simpledialog.askinteger("批量获取", "请输入要获取的编号数量:", 
                                          minvalue=1, maxvalue=100)
            if count is None:
                return
            
            self.get_batch_btn.config(state="disabled")
            numbers = self.manager.get_next_number(count)
            
            if count <= 10:
                numbers_str = ", ".join(map(str, numbers))
            else:
                numbers_str = f"{numbers[0]} ~ {numbers[-1]} (共{count}个)"
            
            self.show_message(f"✅ 成功获取编号: {numbers_str}")
            self.show_bubble_message(f"批量获取: {numbers_str}")
            self.refresh_status()
            
        except Exception as e:
            messagebox.showerror("错误", f"批量获取编号失败: {str(e)}")
        finally:
            self.get_batch_btn.config(state="normal")
    
    def refresh_status(self):
        """刷新状态"""
        try:
            status = self.manager.get_current_status()
            
            if "error" in status:
                self.next_number_var.set("连接失败")
                self.shared_path_var.set(f"错误: {status['error']}")
                return
            
            self.next_number_var.set(str(status["next_number"]))
            self.shared_path_var.set(status["shared_path"])
            
            # 更新历史记录
            self.update_history_table(status["recent_logs"])
            
        except Exception as e:
            self.next_number_var.set("错误")
            self.shared_path_var.set(f"刷新失败: {str(e)}")
    
    def update_history_table(self, logs):
        """更新历史记录表格"""
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        for log in reversed(logs):
            if log.get("count", 1) == 0:
                continue
                
            count = log.get("count", 1)
            if count == 1:
                number_display = str(log["number"])
            else:
                end_number = log["number"] + count - 1
                number_display = f"{log['number']}~{end_number}"
            
            self.history_tree.insert("", 0, values=(
                number_display, count, log["user"], log["hostname"], log["date"], log["time"]
            ))
    
    def show_message(self, message):
        """显示消息"""
        self.message_var.set(message)
        self.root.after(3000, lambda: self.message_var.set(""))
    
    def show_bubble_message(self, message):
        """显示简化版冒泡消息"""
        bubble = tk.Toplevel(self.root)
        bubble.title("通知")
        bubble.geometry("300x80")
        bubble.configure(bg='#3498db')
        bubble.resizable(False, False)
        
        x = self.root.winfo_x() + self.root.winfo_width() - 320
        y = self.root.winfo_y() + self.root.winfo_height() - 100
        bubble.geometry(f"300x80+{x}+{y}")
        
        bubble.attributes("-topmost", True)
        
        tk.Label(bubble, text="🎉 编号分配成功", 
                font=('Microsoft YaHei UI', 10, 'bold'),
                fg='white', bg='#3498db').pack(pady=(10, 5))
        
        tk.Label(bubble, text=message,
                font=('Microsoft YaHei UI', 9),
                fg='white', bg='#3498db').pack()
        
        bubble.after(2000, bubble.destroy)
    
    def show_settings(self):
        """显示设置对话框"""
        settings = tk.Toplevel(self.root)
        settings.title("设置")
        settings.geometry("350x200")
        settings.configure(bg='#f5f5f5')
        settings.resizable(False, False)
        
        settings.transient(self.root)
        settings.grab_set()
        
        frame = tk.Frame(settings, bg='#f5f5f5')
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        tk.Label(frame, text="⚙️ 系统设置", 
                font=('Microsoft YaHei UI', 14, 'bold'),
                fg='#2c3e50', bg='#f5f5f5').pack(pady=(0, 20))
        
        if not self.manager.config.get("initialized", False):
            tk.Label(frame, text="设置起始编号:", 
                    font=('Microsoft YaHei UI', 10),
                    fg='#2c3e50', bg='#f5f5f5').pack()
            
            start_number_var = tk.StringVar(value=str(self.manager.config["start_number"]))
            entry = tk.Entry(frame, textvariable=start_number_var, 
                           font=('Microsoft YaHei UI', 11), width=20)
            entry.pack(pady=(5, 15))
            
            def set_start():
                try:
                    start_num = int(start_number_var.get())
                    self.manager.set_start_number(start_num)
                    messagebox.showinfo("成功", f"起始编号已设置为: {start_num}")
                    settings.destroy()
                    self.refresh_status()
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的数字")
                except Exception as e:
                    messagebox.showerror("错误", str(e))
            
            tk.Button(frame, text="设置", command=set_start,
                     font=('Microsoft YaHei UI', 10),
                     bg='#3498db', fg='white',
                     relief='flat', padx=20, pady=8).pack(pady=(0, 10))
        else:
            tk.Label(frame, text=f"当前起始编号: {self.manager.config['start_number']}", 
                    font=('Microsoft YaHei UI', 11),
                    fg='#2c3e50', bg='#f5f5f5').pack(pady=(0, 10))
            tk.Label(frame, text="系统已初始化", 
                    font=('Microsoft YaHei UI', 10),
                    fg='#7f8c8d', bg='#f5f5f5').pack()
        
        tk.Button(frame, text="关闭", command=settings.destroy,
                 font=('Microsoft YaHei UI', 10),
                 bg='#ecf0f1', fg='#2c3e50',
                 relief='flat', padx=20, pady=8).pack(side=tk.BOTTOM)
    
    def update_status_loop(self):
        """状态更新循环"""
        while self.running:
            try:
                time.sleep(5)
                if self.running:
                    self.root.after(0, self.refresh_status)
            except Exception:
                break
    
    def on_closing(self):
        """关闭程序"""
        self.running = False
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleModernGUI()
    app.run()
