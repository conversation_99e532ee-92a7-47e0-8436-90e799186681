import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import time
from datetime import datetime
from number_manager import NumberManager

class NumberGeneratorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("编号生成器 v1.0")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 设置现代化主题和样式
        self.setup_modern_theme()

        # 初始化编号管理器
        try:
            self.manager = NumberManager()
        except Exception as e:
            messagebox.showerror("错误", f"初始化失败: {str(e)}")
            self.root.destroy()
            return

        # 创建界面
        self.create_widgets()

        # 启动状态更新线程
        self.running = True
        self.update_thread = threading.Thread(target=self.update_status_loop, daemon=True)
        self.update_thread.start()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_modern_theme(self):
        """设置现代化主题"""
        # 设置窗口背景色
        self.root.configure(bg='#f5f5f5')

        # 创建自定义样式
        style = ttk.Style()

        # 配置现代化样式
        style.configure('Title.TLabel',
                       font=('Microsoft YaHei UI', 20, 'bold'),
                       foreground='#2c3e50',
                       background='#f5f5f5')

        style.configure('BigNumber.TLabel',
                       font=('Microsoft YaHei UI', 24, 'bold'),
                       foreground='#3498db',
                       background='white',
                       relief='flat',
                       borderwidth=0)

        style.configure('Card.TFrame',
                       background='white',
                       relief='flat',
                       borderwidth=1)

        style.configure('Modern.TButton',
                       font=('Microsoft YaHei UI', 10),
                       padding=(20, 10))

        style.configure('Primary.TButton',
                       font=('Microsoft YaHei UI', 12, 'bold'),
                       padding=(25, 15))

        style.configure('Info.TLabel',
                       font=('Microsoft YaHei UI', 10),
                       foreground='#7f8c8d',
                       background='white')

        style.configure('Status.TLabel',
                       font=('Microsoft YaHei UI', 11),
                       foreground='#2c3e50',
                       background='white')

        # 配置按钮颜色
        style.map('Primary.TButton',
                 background=[('active', '#2980b9'), ('!active', '#3498db')],
                 foreground=[('active', 'white'), ('!active', 'white')])

        style.map('Modern.TButton',
                 background=[('active', '#ecf0f1'), ('!active', 'white')],
                 foreground=[('active', '#2c3e50'), ('!active', '#34495e')])
    
    def create_widgets(self):
        """创建现代化界面组件"""
        # 主容器 - 使用Canvas实现渐变背景
        self.main_canvas = tk.Canvas(self.root, bg='#f5f5f5', highlightthickness=0)
        self.main_canvas.pack(fill=tk.BOTH, expand=True)

        # 创建渐变背景
        self.create_gradient_background()

        # 主内容框架
        main_frame = tk.Frame(self.main_canvas, bg='#f5f5f5')
        self.main_canvas.create_window(450, 350, window=main_frame, anchor='center')

        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#f5f5f5')
        title_frame.pack(pady=(30, 40))

        title_label = ttk.Label(title_frame, text="互感器评审编号生成器", style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="多人协作 • 实时同步 • 安全可靠",
                                  font=('Microsoft YaHei UI', 10),
                                  foreground='#7f8c8d', background='#f5f5f5')
        subtitle_label.pack(pady=(5, 0))

        # 编号显示卡片 - 最突出的区域
        number_card = self.create_card_frame(main_frame)
        number_card.pack(pady=(0, 30), padx=40, fill=tk.X)

        # 编号显示区域
        number_display_frame = tk.Frame(number_card, bg='white')
        number_display_frame.pack(pady=30, padx=30)

        ttk.Label(number_display_frame, text="下一个编号", style='Info.TLabel').pack()

        # 大号编号显示
        self.next_number_var = tk.StringVar(value="加载中...")
        number_frame = tk.Frame(number_display_frame, bg='#e8f4fd', relief='flat', bd=0)
        number_frame.pack(pady=(10, 5), padx=20, fill=tk.X)

        self.number_label = tk.Label(number_frame, textvariable=self.next_number_var,
                                    font=('Microsoft YaHei UI', 28, 'bold'),
                                    fg='#2980b9', bg='#e8f4fd',
                                    pady=15, padx=20)
        self.number_label.pack()

        # 状态信息
        status_info_frame = tk.Frame(number_display_frame, bg='white')
        status_info_frame.pack(pady=(10, 0))

        ttk.Label(status_info_frame, text="共享路径:", style='Info.TLabel').pack(side=tk.LEFT)
        self.shared_path_var = tk.StringVar()
        ttk.Label(status_info_frame, textvariable=self.shared_path_var,
                 style='Info.TLabel').pack(side=tk.LEFT, padx=(5, 0))
        
        # 操作按钮区域
        button_card = self.create_card_frame(main_frame)
        button_card.pack(pady=(0, 30), padx=40, fill=tk.X)

        button_container = tk.Frame(button_card, bg='white')
        button_container.pack(pady=25, padx=30)

        # 主要操作按钮
        primary_buttons_frame = tk.Frame(button_container, bg='white')
        primary_buttons_frame.pack(pady=(0, 15))

        # 获取编号按钮 - 主要按钮
        self.get_number_btn = tk.Button(primary_buttons_frame, text="📋 获取下一个编号",
                                       command=self.get_single_number,
                                       font=('Microsoft YaHei UI', 12, 'bold'),
                                       bg='#3498db', fg='white',
                                       relief='flat', bd=0,
                                       padx=30, pady=12,
                                       cursor='hand2')
        self.get_number_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 批量获取按钮
        self.get_batch_btn = tk.Button(primary_buttons_frame, text="📊 批量获取编号",
                                      command=self.get_batch_numbers,
                                      font=('Microsoft YaHei UI', 11),
                                      bg='#2ecc71', fg='white',
                                      relief='flat', bd=0,
                                      padx=25, pady=10,
                                      cursor='hand2')
        self.get_batch_btn.pack(side=tk.LEFT)

        # 次要操作按钮
        secondary_buttons_frame = tk.Frame(button_container, bg='white')
        secondary_buttons_frame.pack()

        # 刷新按钮
        refresh_btn = tk.Button(secondary_buttons_frame, text="🔄 刷新状态",
                               command=self.refresh_status,
                               font=('Microsoft YaHei UI', 10),
                               bg='white', fg='#34495e',
                               relief='flat', bd=1,
                               padx=20, pady=8,
                               cursor='hand2')
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 设置按钮
        settings_btn = tk.Button(secondary_buttons_frame, text="⚙️ 设置",
                                command=self.show_settings,
                                font=('Microsoft YaHei UI', 10),
                                bg='white', fg='#34495e',
                                relief='flat', bd=1,
                                padx=20, pady=8,
                                cursor='hand2')
        settings_btn.pack(side=tk.LEFT)

        # 为按钮添加悬停效果
        self.add_button_hover_effects()
        
        # 编号历史区域
        history_card = self.create_card_frame(main_frame)
        history_card.pack(pady=(0, 20), padx=40, fill=tk.BOTH, expand=True)

        # 历史记录标题
        history_header = tk.Frame(history_card, bg='white')
        history_header.pack(fill=tk.X, padx=20, pady=(20, 10))

        ttk.Label(history_header, text="📋 最近编号分配记录",
                 font=('Microsoft YaHei UI', 14, 'bold'),
                 foreground='#2c3e50', background='white').pack(side=tk.LEFT)

        # 表格容器
        table_frame = tk.Frame(history_card, bg='white')
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # 创建现代化表格
        columns = ("编号", "数量", "用户", "计算机", "日期", "时间")
        self.history_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=8)

        # 设置表格样式
        style = ttk.Style()
        style.configure("Treeview",
                       font=('Microsoft YaHei UI', 10),
                       rowheight=25)
        style.configure("Treeview.Heading",
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground='#2c3e50')

        # 设置列标题和宽度
        for col in columns:
            self.history_tree.heading(col, text=col)
            if col == "编号":
                self.history_tree.column(col, width=100, anchor=tk.CENTER)
            elif col == "数量":
                self.history_tree.column(col, width=70, anchor=tk.CENTER)
            elif col == "用户":
                self.history_tree.column(col, width=120, anchor=tk.CENTER)
            elif col == "计算机":
                self.history_tree.column(col, width=140, anchor=tk.CENTER)
            elif col == "日期":
                self.history_tree.column(col, width=110, anchor=tk.CENTER)
            elif col == "时间":
                self.history_tree.column(col, width=90, anchor=tk.CENTER)

        # 添加现代化滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)

        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 消息显示区域
        message_frame = tk.Frame(main_frame, bg='#f5f5f5')
        message_frame.pack(pady=(10, 20))

        self.message_var = tk.StringVar()
        self.message_label = tk.Label(message_frame, textvariable=self.message_var,
                                     font=('Microsoft YaHei UI', 11),
                                     fg='#27ae60', bg='#f5f5f5')
        self.message_label.pack()

        # 绑定窗口大小变化事件
        self.root.bind('<Configure>', self.on_window_resize)

        # 初始化状态
        self.refresh_status()

    def create_gradient_background(self):
        """创建渐变背景"""
        # 简单的渐变效果
        self.main_canvas.create_rectangle(0, 0, 900, 700, fill='#f8f9fa', outline='')

        # 添加一些装饰性元素
        for i in range(5):
            x = 100 + i * 150
            y = 50 + i * 20
            self.main_canvas.create_oval(x, y, x+20, y+20, fill='#e3f2fd', outline='')

    def create_card_frame(self, parent):
        """创建卡片式框架"""
        card = tk.Frame(parent, bg='white', relief='flat', bd=0)

        # 添加阴影效果（通过多层框架模拟）
        shadow = tk.Frame(parent, bg='#e0e0e0', relief='flat', bd=0)
        shadow.place(in_=card, x=3, y=3, relwidth=1, relheight=1)

        return card

    def add_button_hover_effects(self):
        """为按钮添加悬停效果"""
        def on_enter(event, button, hover_color):
            button.configure(bg=hover_color)

        def on_leave(event, button, normal_color):
            button.configure(bg=normal_color)

        # 主要按钮悬停效果
        self.get_number_btn.bind("<Enter>", lambda e: on_enter(e, self.get_number_btn, '#2980b9'))
        self.get_number_btn.bind("<Leave>", lambda e: on_leave(e, self.get_number_btn, '#3498db'))

        self.get_batch_btn.bind("<Enter>", lambda e: on_enter(e, self.get_batch_btn, '#27ae60'))
        self.get_batch_btn.bind("<Leave>", lambda e: on_leave(e, self.get_batch_btn, '#2ecc71'))

    def on_window_resize(self, event):
        """窗口大小变化时的处理"""
        if event.widget == self.root:
            # 重新计算Canvas大小
            canvas_width = self.root.winfo_width()
            canvas_height = self.root.winfo_height()
            self.main_canvas.configure(width=canvas_width, height=canvas_height)

            # 重新绘制背景
            self.main_canvas.delete("background")
            self.create_gradient_background()

    def get_single_number(self):
        """获取单个编号"""
        try:
            self.get_number_btn.config(state="disabled")
            number = self.manager.get_next_number()
            
            # 显示成功消息
            self.show_message(f"成功获取编号: {number}", "success")
            
            # 显示冒泡消息
            self.show_bubble_message(f"获取编号: {number}")
            
            # 刷新状态
            self.refresh_status()
            
        except Exception as e:
            messagebox.showerror("错误", f"获取编号失败: {str(e)}")
        finally:
            self.get_number_btn.config(state="normal")
    
    def get_batch_numbers(self):
        """批量获取编号"""
        try:
            count = simpledialog.askinteger("批量获取", "请输入要获取的编号数量:", 
                                          minvalue=1, maxvalue=100)
            if count is None:
                return
            
            self.get_batch_btn.config(state="disabled")
            numbers = self.manager.get_next_number(count)
            
            # 显示结果
            if count <= 10:
                numbers_str = ", ".join(map(str, numbers))
            else:
                numbers_str = f"{numbers[0]} ~ {numbers[-1]} (共{count}个)"
            
            self.show_message(f"成功获取编号: {numbers_str}", "success")
            self.show_bubble_message(f"批量获取: {numbers_str}")
            
            # 刷新状态
            self.refresh_status()
            
        except Exception as e:
            messagebox.showerror("错误", f"批量获取编号失败: {str(e)}")
        finally:
            self.get_batch_btn.config(state="normal")
    
    def refresh_status(self):
        """刷新状态显示"""
        try:
            status = self.manager.get_current_status()
            
            if "error" in status:
                self.next_number_var.set("连接失败")
                self.shared_path_var.set(f"错误: {status['error']}")
                return
            
            self.next_number_var.set(str(status["next_number"]))
            self.shared_path_var.set(status["shared_path"])
            
            # 更新历史记录
            self.update_history_table(status["recent_logs"])
            
        except Exception as e:
            self.next_number_var.set("错误")
            self.shared_path_var.set(f"刷新失败: {str(e)}")
    
    def update_history_table(self, logs):
        """更新历史记录表格"""
        # 清空现有数据
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # 添加新数据（倒序显示，最新的在上面）
        for log in reversed(logs):
            if log.get("count", 1) == 0:  # 跳过重置操作记录
                continue
                
            count = log.get("count", 1)
            if count == 1:
                number_display = str(log["number"])
            else:
                end_number = log["number"] + count - 1
                number_display = f"{log['number']}~{end_number}"
            
            self.history_tree.insert("", 0, values=(
                number_display,
                count,
                log["user"],
                log["hostname"],
                log["date"],
                log["time"]
            ))
    
    def show_message(self, message, msg_type="info"):
        """显示现代化消息"""
        # 根据消息类型设置颜色
        if msg_type == "success":
            color = "#27ae60"
            icon = "✅"
        elif msg_type == "error":
            color = "#e74c3c"
            icon = "❌"
        else:
            color = "#3498db"
            icon = "ℹ️"

        self.message_label.config(fg=color)
        self.message_var.set(f"{icon} {message}")

        # 添加消息动画效果
        self.animate_message()

        # 3秒后清除消息
        self.root.after(3000, lambda: self.message_var.set(""))

    def animate_message(self):
        """消息动画效果"""
        original_font = self.message_label.cget('font')

        def pulse():
            # 简单的脉冲效果
            self.message_label.configure(font=('Microsoft YaHei UI', 12, 'bold'))
            self.root.after(200, lambda: self.message_label.configure(font=original_font))

        pulse()
    
    def show_bubble_message(self, message):
        """显示现代化冒泡消息"""
        bubble = tk.Toplevel(self.root)
        bubble.title("编号分配通知")
        bubble.geometry("350x120")
        bubble.resizable(False, False)
        bubble.configure(bg='white')

        # 设置窗口位置（右下角）
        x = self.root.winfo_x() + self.root.winfo_width() - 370
        y = self.root.winfo_y() + self.root.winfo_height() - 140
        bubble.geometry(f"350x120+{x}+{y}")

        # 设置为工具窗口
        bubble.attributes("-topmost", True)
        bubble.overrideredirect(True)  # 无边框窗口

        # 创建现代化卡片样式
        main_frame = tk.Frame(bubble, bg='white', relief='flat', bd=0)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # 添加边框效果
        border_frame = tk.Frame(main_frame, bg='#3498db', relief='flat', bd=0)
        border_frame.pack(fill=tk.BOTH, expand=True)

        content_frame = tk.Frame(border_frame, bg='white', relief='flat', bd=0)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

        # 标题区域
        title_frame = tk.Frame(content_frame, bg='#3498db')
        title_frame.pack(fill=tk.X)

        tk.Label(title_frame, text="🎉 编号分配成功",
                font=('Microsoft YaHei UI', 11, 'bold'),
                fg='white', bg='#3498db', pady=8).pack()

        # 内容区域
        content_area = tk.Frame(content_frame, bg='white')
        content_area.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        tk.Label(content_area, text=message,
                font=('Microsoft YaHei UI', 10),
                fg='#2c3e50', bg='white').pack()

        tk.Label(content_area, text=datetime.now().strftime("时间: %H:%M:%S"),
                font=('Microsoft YaHei UI', 9),
                fg='#7f8c8d', bg='white').pack(pady=(5, 0))

        # 添加淡入淡出动画效果
        self.animate_bubble(bubble)

        # 3秒后自动关闭
        bubble.after(3000, lambda: self.close_bubble(bubble))

    def animate_bubble(self, bubble):
        """冒泡消息动画效果"""
        # 简单的淡入效果
        bubble.attributes('-alpha', 0.0)

        def fade_in(alpha=0.0):
            alpha += 0.1
            if alpha <= 1.0:
                bubble.attributes('-alpha', alpha)
                bubble.after(50, lambda: fade_in(alpha))

        fade_in()

    def close_bubble(self, bubble):
        """关闭冒泡消息"""
        def fade_out(alpha=1.0):
            alpha -= 0.1
            if alpha >= 0.0:
                bubble.attributes('-alpha', alpha)
                bubble.after(50, lambda: fade_out(alpha))
            else:
                bubble.destroy()

        fade_out()
    
    def show_settings(self):
        """显示现代化设置对话框"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("系统设置")
        settings_window.geometry("450x300")
        settings_window.resizable(False, False)
        settings_window.configure(bg='#f5f5f5')

        # 居中显示
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 计算居中位置
        x = self.root.winfo_x() + (self.root.winfo_width() - 450) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - 300) // 2
        settings_window.geometry(f"450x300+{x}+{y}")

        # 主容器
        main_container = tk.Frame(settings_window, bg='#f5f5f5')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_container, bg='#f5f5f5')
        title_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(title_frame, text="⚙️ 系统设置",
                font=('Microsoft YaHei UI', 16, 'bold'),
                fg='#2c3e50', bg='#f5f5f5').pack()

        # 设置卡片
        settings_card = tk.Frame(main_container, bg='white', relief='flat', bd=0)
        settings_card.pack(fill=tk.BOTH, expand=True)

        # 卡片内容
        content_frame = tk.Frame(settings_card, bg='white')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # 起始编号设置（仅在未初始化时显示）
        if not self.manager.config.get("initialized", False):
            tk.Label(content_frame, text="设置起始编号",
                    font=('Microsoft YaHei UI', 12, 'bold'),
                    fg='#2c3e50', bg='white').pack(anchor=tk.W)

            tk.Label(content_frame, text="请输入编号生成的起始数值（如：1001）",
                    font=('Microsoft YaHei UI', 10),
                    fg='#7f8c8d', bg='white').pack(anchor=tk.W, pady=(5, 15))

            # 输入框
            input_frame = tk.Frame(content_frame, bg='white')
            input_frame.pack(fill=tk.X, pady=(0, 20))

            start_number_var = tk.StringVar(value=str(self.manager.config["start_number"]))
            start_entry = tk.Entry(input_frame, textvariable=start_number_var,
                                  font=('Microsoft YaHei UI', 12),
                                  relief='flat', bd=1,
                                  highlightthickness=1,
                                  highlightcolor='#3498db')
            start_entry.pack(fill=tk.X, ipady=8)

            def set_start_number():
                try:
                    start_num = int(start_number_var.get())
                    if start_num <= 0:
                        messagebox.showerror("错误", "起始编号必须大于0")
                        return

                    self.manager.set_start_number(start_num)
                    messagebox.showinfo("设置成功", f"起始编号已设置为: {start_num}")
                    settings_window.destroy()
                    self.refresh_status()
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的数字")
                except Exception as e:
                    messagebox.showerror("错误", str(e))

            # 按钮区域
            button_frame = tk.Frame(content_frame, bg='white')
            button_frame.pack(fill=tk.X, pady=(10, 0))

            set_btn = tk.Button(button_frame, text="✅ 设置起始编号",
                               command=set_start_number,
                               font=('Microsoft YaHei UI', 11, 'bold'),
                               bg='#3498db', fg='white',
                               relief='flat', bd=0,
                               padx=20, pady=10,
                               cursor='hand2')
            set_btn.pack(side=tk.LEFT)

        else:
            # 已初始化状态
            tk.Label(content_frame, text="系统状态",
                    font=('Microsoft YaHei UI', 12, 'bold'),
                    fg='#2c3e50', bg='white').pack(anchor=tk.W)

            status_frame = tk.Frame(content_frame, bg='#e8f5e8', relief='flat', bd=0)
            status_frame.pack(fill=tk.X, pady=(10, 20))

            tk.Label(status_frame, text=f"✅ 系统已初始化",
                    font=('Microsoft YaHei UI', 11),
                    fg='#27ae60', bg='#e8f5e8', pady=10).pack()

            tk.Label(content_frame, text=f"当前起始编号: {self.manager.config['start_number']}",
                    font=('Microsoft YaHei UI', 11),
                    fg='#2c3e50', bg='white').pack(anchor=tk.W, pady=(0, 10))

            tk.Label(content_frame, text="系统已初始化，无法修改起始编号",
                    font=('Microsoft YaHei UI', 10),
                    fg='#7f8c8d', bg='white').pack(anchor=tk.W)

        # 底部按钮
        bottom_frame = tk.Frame(main_container, bg='#f5f5f5')
        bottom_frame.pack(fill=tk.X, pady=(20, 0))

        close_btn = tk.Button(bottom_frame, text="关闭",
                             command=settings_window.destroy,
                             font=('Microsoft YaHei UI', 10),
                             bg='white', fg='#34495e',
                             relief='flat', bd=1,
                             padx=20, pady=8,
                             cursor='hand2')
        close_btn.pack(side=tk.RIGHT)
    
    def update_status_loop(self):
        """状态更新循环（后台线程）"""
        while self.running:
            try:
                # 每5秒刷新一次状态
                time.sleep(5)
                if self.running:
                    self.root.after(0, self.refresh_status)
            except Exception:
                break
    
    def on_closing(self):
        """关闭程序"""
        self.running = False
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = NumberGeneratorGUI()
    app.run()
